# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import AccessError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class ResUsers(models.Model):
    _inherit = 'res.users'

    # Computed field to check if current user has limited admin rights
    has_limited_admin_rights = fields.Boolean(
        string='Has Limited Admin Rights',
        compute='_compute_has_limited_admin_rights',
        help='True if user has Access Rights but not full admin privileges'
    )

    @api.depends_context('uid')
    def _compute_has_limited_admin_rights(self):
        """
        Compute if current user has limited admin rights.
        Limited admin rights means:
        - Has base.group_user (Access Rights)
        - Does NOT have base.group_system (Settings/Technical)
        - Does NOT have base.group_erp_manager (Full Admin)
        """
        for record in self:
            current_user = self.env.user

            # Check if user has basic access rights
            has_access_rights = current_user.has_group('base.group_user')

            # Check if user has full admin privileges
            has_full_admin = (
                current_user.has_group('base.group_system') or
                current_user.has_group('base.group_erp_manager') or
                self.env.is_superuser()
            )

            # Limited admin rights = has access rights but not full admin
            record.has_limited_admin_rights = has_access_rights and not has_full_admin

    def _check_user_creation_rights(self):
        """
        Check if current user has rights to create/modify users.
        Returns True if user can create users, False otherwise.
        """
        current_user = self.env.user

        # Superuser always has rights
        if self.env.is_superuser():
            return True

        # Full admin users have rights
        if (current_user.has_group('base.group_system') or
            current_user.has_group('base.group_erp_manager')):
            return True

        # Limited admin users have restricted rights
        if current_user.has_group('base.group_user'):
            return True

        return False

    def _get_allowed_fields_for_limited_admin(self):
        """
        Get list of fields that limited admin users can modify.
        Only 'name' and 'email' are allowed for limited admin users.
        """
        return ['name', 'email']

    def _is_limited_admin_user(self):
        """
        Check if current user is a limited admin user.
        Limited admin = has Access Rights but NOT Settings
        """
        current_user = self.env.user

        # Check if user has Access Rights (base.group_user)
        has_access_rights = current_user.has_group('base.group_user')

        # Check if user has Settings permission (base.group_system)
        has_settings = current_user.has_group('base.group_system')

        # Check if user has ERP Manager permission
        has_erp_manager = current_user.has_group('base.group_erp_manager')

        # Limited admin = Access Rights but NOT Settings or ERP Manager
        return (has_access_rights and
                not has_settings and
                not has_erp_manager and
                not self.env.is_superuser())

    def _validate_groups_assignment(self, vals):
        """
        Validate groups assignment for limited admin users.
        Prevents privilege escalation through group assignments.
        """
        if 'groups_id' not in vals:
            return True

        if not self._is_limited_admin_user():
            return True

        # Log the attempt for security monitoring
        _logger.warning(
            "Limited admin user %s (ID: %s) attempted to assign groups. "
            "This action was blocked for security reasons.",
            self.env.user.name, self.env.user.id
        )

        # Remove groups_id from vals to prevent unauthorized assignment
        del vals['groups_id']
        return True

    @api.model
    def create(self, vals):
        """
        Override create to enforce limited admin restrictions.
        """
        if not self._check_user_creation_rights():
            raise AccessError(_("You don't have the rights to create users."))

        # Validate and clean vals for limited admin users
        if self._is_limited_admin_user():
            self._validate_groups_assignment(vals)
            self._clean_vals_for_limited_admin(vals)
            # Set login from email if not provided
            if 'email' in vals and 'login' not in vals:
                vals['login'] = vals['email']

        return super(ResUsers, self).create(vals)

    def _clean_vals_for_limited_admin(self, vals):
        """
        Clean vals dictionary for limited admin users.
        Remove unauthorized fields and log attempts.
        """
        allowed_fields = self._get_allowed_fields_for_limited_admin()
        system_fields = [
            'partner_id', 'company_id', 'company_ids', 'id'  # System managed fields
        ]

        unauthorized_fields = []
        for field_name in list(vals.keys()):
            if field_name not in allowed_fields and field_name not in system_fields:
                unauthorized_fields.append(field_name)
                del vals[field_name]

        if unauthorized_fields:
            _logger.warning(
                "Limited admin user %s (ID: %s) attempted to modify unauthorized fields: %s. "
                "These fields were ignored for security reasons.",
                self.env.user.name, self.env.user.id, ', '.join(unauthorized_fields)
            )

    def write(self, vals):
        """
        Override write to enforce limited admin restrictions.
        """
        # Validate and clean vals for limited admin users
        if self._is_limited_admin_user():
            self._validate_groups_assignment(vals)
            self._clean_vals_for_limited_admin(vals)

        return super(ResUsers, self).write(vals)

    @api.constrains('groups_id')
    def _check_groups_assignment(self):
        """
        Additional constraint to prevent unauthorized group assignments.
        This provides an extra layer of security.
        """
        if self._is_limited_admin_user():
            # Check if any user being modified has elevated privileges
            for user in self:
                if (user.has_group('base.group_system') or
                    user.has_group('base.group_erp_manager')):
                    raise ValidationError(_(
                        "Limited admin users cannot assign elevated privileges. "
                        "Contact your system administrator for assistance."
                    ))

    @api.model
    def fields_get(self, allfields=None, attributes=None):
        """
        Override fields_get to modify field attributes for limited admin users.
        """
        res = super(ResUsers, self).fields_get(allfields, attributes)

        # If user has limited admin rights, make most fields readonly
        if self._is_limited_admin_user():
            allowed_fields = self._get_allowed_fields_for_limited_admin()
            readonly_fields = [
                'active', 'groups_id', 'lang', 'tz', 'company_id', 'company_ids',
                'login', 'phone', 'mobile', 'signature', 'action_id', 'share',
                'partner_id', 'image_1920', 'notification_type'
            ]

            for field_name in readonly_fields:
                if field_name in res and field_name not in allowed_fields:
                    res[field_name]['readonly'] = True

        return res

    @api.model
    def default_get(self, fields_list):
        """
        Override default_get to set appropriate defaults for limited admin users.
        """
        res = super(ResUsers, self).default_get(fields_list)

        # For limited admin users, ensure safe defaults
        if self._is_limited_admin_user():
            # Don't set any groups by default
            if 'groups_id' in res:
                del res['groups_id']

            # Set active to True by default (safe default)
            if 'active' in fields_list:
                res['active'] = True

            # Set share to False by default (internal user)
            if 'share' in fields_list:
                res['share'] = False

        return res
