# User Creation Limited Rights

## Overview

This Odoo 15 Community module provides controlled user creation capabilities for users who have "Access Rights" permission in Administration but not full "Settings" or "Technical" permissions.

## Features

### For Limited Admin Users
- **Restricted User Creation**: Can create new users with limited field access
- **Name and Login Only**: Only `name` and `login` fields are editable
- **Readonly Protection**: All other fields (groups, active status, language, timezone, etc.) are readonly
- **Security Enforcement**: Backend protection against unauthorized modifications

### Security Features
- **Backend Protection**: Prevents unauthorized group assignments via API calls
- **Logging**: Security attempts are logged for monitoring
- **Validation**: Multiple layers of validation to prevent privilege escalation
- **Clean UX**: Unauthorized fields are silently removed instead of showing errors

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list: `Settings > Apps > Update Apps List`
3. Search for "User Creation Limited Rights"
4. Click Install

## Usage

### For System Administrators
1. Assign users to the "Access Rights" group in Administration
2. Ensure they do NOT have "Settings" or "Technical" permissions
3. These users will now have limited user creation capabilities

### For Limited Admin Users
1. Navigate to `Settings > Users & Companies > Users`
2. Click "Create" to add a new user
3. Fill in the Name and Login fields
4. All other fields will be readonly
5. Save the user

## Technical Details

### User Permission Levels
- **Full Admin**: `base.group_system` or `base.group_erp_manager`
- **Limited Admin**: `base.group_user` without system/erp_manager groups
- **Regular User**: No special permissions

### Protected Fields
The following fields are readonly for limited admin users:
- `active` - User activation status
- `groups_id` - User groups and permissions
- `lang` - User language
- `tz` - User timezone
- `company_id` - User company
- `company_ids` - User companies
- `login` - User login (automatically set from email)
- `phone` - User phone
- `mobile` - User mobile
- `signature` - User signature
- `action_id` - User action
- `share` - Portal user flag

### Editable Fields
Limited admin users can only modify:
- `name` - User display name
- `email` - User email address (login is automatically set from email)

## Security Implementation

### Backend Protection
- **Method Override**: `create()` and `write()` methods are overridden
- **Field Validation**: Unauthorized fields are automatically removed
- **Group Protection**: Group assignments are blocked and logged
- **Constraint Validation**: Additional constraints prevent privilege escalation

### Frontend Protection
- **View Inheritance**: User form views are inherited and modified
- **Attrs Usage**: Dynamic readonly attributes based on user permissions
- **Computed Fields**: Real-time permission checking

### Logging
All security-related attempts are logged with:
- User ID and name
- Attempted action
- Timestamp
- Security violation details

## Compatibility

- **Odoo Version**: 15.0 Community Edition
- **Dependencies**: `base` module only
- **Conflicts**: None known
- **Extensibility**: Clean code structure for future enhancements

## Configuration

No additional configuration required. The module works automatically based on user group memberships.

## Troubleshooting

### Common Issues

1. **Users can't create users**
   - Ensure they have `base.group_user` permission
   - Check they don't have conflicting permissions

2. **All fields are readonly**
   - Verify user has correct group assignments
   - Check for conflicting modules

3. **Groups still editable**
   - Clear browser cache
   - Restart Odoo server
   - Check module installation

### Debug Mode
Enable developer mode to see computed field values and debug permission issues.

## Support

For issues or questions:
1. Check the logs for security violations
2. Verify user group memberships
3. Test with different user permission levels

## License

LGPL-3 License

## Author

Custom Development Team
