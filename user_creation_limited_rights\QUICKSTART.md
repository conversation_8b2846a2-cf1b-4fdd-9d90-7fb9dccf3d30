# Quick Start Guide

## 🚀 5-Minute Setup

### Step 1: Install Module
1. Copy `user_creation_limited_rights` folder to your Odoo addons directory
2. Restart Odoo server
3. Go to `Apps` → Search "User Creation Limited Rights" → Install

### Step 2: Create Limited Admin User
1. Go to `Settings` → `Users & Companies` → `Users`
2. Click `Create`
3. Fill user details:
   - **Name**: HR Manager
   - **Login**: <EMAIL>
   - **Groups**: Only select `Administration / Access Rights`
   - **Important**: Do NOT select `Settings` or `Technical Features`
4. Save

### Step 3: Test Limited Admin
1. Logout and login as the new limited admin user
2. Go to `Settings` → `Users & Companies` → `Users`
3. Click `Create`
4. Notice:
   - ✅ You can edit `Name` and `Email Address` fields
   - 🔒 All other fields including Groups/Permissions are readonly
   - ℹ️ Blue info box shows "Limited Admin Mode"
   - 🔄 Login is automatically set from Email Address

## ✅ Verification Checklist

- [ ] Module installed without errors
- [ ] Limited admin user created with only `base.group_user`
- [ ] Limited admin can access user creation form
- [ ] Only Name and Email Address fields are editable
- [ ] Info message appears for limited admin users
- [ ] User creation works with allowed fields
- [ ] Full admin users retain all capabilities

## 🔧 Common Use Cases

### HR Department
```
User: HR Manager
Groups: Administration/Access Rights + Human Resources/Manager
Can: Create users for new employees
Cannot: Assign admin privileges or modify system settings
```

### Department Manager
```
User: Sales Manager
Groups: Administration/Access Rights + Sales/Manager
Can: Create users for team members
Cannot: Access technical settings or assign groups
```

### IT Support (Limited)
```
User: IT Support Level 1
Groups: Administration/Access Rights
Can: Create basic user accounts
Cannot: Assign technical or admin privileges
```

## 🛡️ Security Features

### What's Protected
- ✅ Group assignments blocked
- ✅ Admin privilege escalation prevented
- ✅ API manipulation blocked
- ✅ Security violations logged

### What's Allowed
- ✅ User name modification
- ✅ Email address setting (login auto-set)
- ✅ Basic user creation
- ✅ View existing users

## 🔍 Troubleshooting

### Issue: All fields are editable
**Solution**: Verify user has only `base.group_user` group

### Issue: Cannot create users
**Solution**: Ensure user has `Administration / Access Rights` permission

### Issue: Module won't install
**Solution**: Check server logs and restart Odoo

### Issue: Changes not visible
**Solution**: Clear browser cache and refresh

## 📊 Monitoring

### Check Security Logs
```bash
tail -f /var/log/odoo/odoo.log | grep "Limited admin user"
```

### Verify User Permissions
1. Go to `Settings` → `Users & Companies` → `Users`
2. Open user record
3. Check `Access Rights` tab
4. Verify only allowed groups are assigned

## 🎯 Best Practices

1. **Regular Audits**: Review user permissions monthly
2. **Training**: Educate limited admin users about restrictions
3. **Monitoring**: Check logs for security violations
4. **Documentation**: Keep track of who has limited admin access
5. **Testing**: Regularly test user creation process

## 📞 Quick Support

### Before Reporting Issues
1. Check user group assignments
2. Review server logs
3. Test with different users
4. Verify module installation

### Common Solutions
- Restart Odoo server
- Clear browser cache
- Check file permissions
- Verify database connection

---

**Need more help?** Check the full documentation in README.md and INSTALL.md files.
