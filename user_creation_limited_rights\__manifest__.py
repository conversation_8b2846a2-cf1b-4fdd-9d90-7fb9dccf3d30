# -*- coding: utf-8 -*-
{
    'name': 'User Creation Limited Rights',
    'version': '********.0',
    'category': 'Administration',
    'summary': 'Control user creation permissions for users with limited admin rights',
    'description': """
User Creation Limited Rights
============================

This module provides controlled user creation capabilities for users who have 
"Access Rights" permission in Administration but not full "Settings" or "Technical" permissions.

Features:
---------
* Users with limited admin rights can create new users
* Only name and login fields are editable for limited admin users
* All other fields (groups, active status, language, timezone, etc.) are readonly
* Backend protection against unauthorized group assignments via API calls
* Full compatibility with users having higher admin privileges

Security:
---------
* Prevents unauthorized privilege escalation
* Protects against manual API manipulation
* Maintains system security while allowing controlled user creation

Compatibility:
--------------
* Odoo 15 Community Edition
* Does not interfere with existing admin functionalities
* Clean and extensible code structure
    """,
    'author': 'Custom Development',
    'website': '',
    'license': 'LGPL-3',
    'depends': ['base'],
    'data': [
        'security/ir.model.access.csv',
        'views/res_users_views.xml',
        'views/assets.xml',
    ],
    'test': [
        'tests/test_user_creation_limited_rights.py',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
