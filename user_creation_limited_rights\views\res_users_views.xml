<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit the main user form view to add limited admin controls -->
        <record id="view_users_form_limited_rights" model="ir.ui.view">
            <field name="name">res.users.form.limited.rights</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">

                <!-- Add the computed field for checking limited admin rights -->
                <xpath expr="//form" position="inside">
                    <field name="has_limited_admin_rights" invisible="1"/>
                </xpath>

                <!-- Add a note for limited admin users -->
                <xpath expr="//field[@name='name']" position="after">
                    <div class="alert alert-info" attrs="{'invisible': [('has_limited_admin_rights', '=', False)]}">
                        <strong>Limited Admin Mode:</strong> You can only modify Name and Email Address fields.
                        Other fields including Groups/Permissions are managed by system administrators for security reasons.
                    </div>
                </xpath>

            </field>
        </record>

    </data>
</odoo>
