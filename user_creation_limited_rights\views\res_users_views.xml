<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Inherit the main user form view to add limited admin controls -->
        <record id="view_users_form_limited_rights" model="ir.ui.view">
            <field name="name">res.users.form.limited.rights</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                
                <!-- Add the computed field for checking limited admin rights -->
                <xpath expr="//form" position="inside">
                    <field name="has_limited_admin_rights" invisible="1"/>
                </xpath>
                
                <!-- Make most fields readonly for limited admin users -->
                
                <!-- Active field -->
                <xpath expr="//field[@name='active']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Groups field -->
                <xpath expr="//field[@name='groups_id']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Language field -->
                <xpath expr="//field[@name='lang']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Timezone field -->
                <xpath expr="//field[@name='tz']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Company field -->
                <xpath expr="//field[@name='company_id']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Companies field -->
                <xpath expr="//field[@name='company_ids']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Email field -->
                <xpath expr="//field[@name='email']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Phone field -->
                <xpath expr="//field[@name='phone']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Mobile field -->
                <xpath expr="//field[@name='mobile']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Signature field -->
                <xpath expr="//field[@name='signature']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Action field -->
                <xpath expr="//field[@name='action_id']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Share field -->
                <xpath expr="//field[@name='share']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
            </field>
        </record>
        
        <!-- Inherit the user preferences form view -->
        <record id="view_users_form_simple_modif_limited_rights" model="ir.ui.view">
            <field name="name">res.users.preferences.form.limited.rights</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
            <field name="arch" type="xml">
                
                <!-- Add the computed field for checking limited admin rights -->
                <xpath expr="//form" position="inside">
                    <field name="has_limited_admin_rights" invisible="1"/>
                </xpath>
                
                <!-- Make language field readonly for limited admin users -->
                <xpath expr="//field[@name='lang']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Make timezone field readonly for limited admin users -->
                <xpath expr="//field[@name='tz']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Make email field readonly for limited admin users -->
                <xpath expr="//field[@name='email']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
                <!-- Make signature field readonly for limited admin users -->
                <xpath expr="//field[@name='signature']" position="attributes">
                    <attribute name="attrs">{'readonly': [('has_limited_admin_rights', '=', True)]}</attribute>
                </xpath>
                
            </field>
        </record>
        
    </data>
</odoo>
