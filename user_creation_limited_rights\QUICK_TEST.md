# Quick Test - User Creation Limited Rights

## 🚀 5-Minute Verification Test

### Step 1: Install Module (if not already done)
```bash
# Restart Odoo server first
sudo systemctl restart odoo

# Then install via UI:
# Apps > Search "User Creation Limited Rights" > Install
```

### Step 2: Create Test User
1. Go to `Settings > Users & Companies > Users`
2. Click `Create`
3. Fill in:
   - **Name**: `Test Limited Admin`
   - **Email**: `<EMAIL>`
   - **Groups**: Select ONLY `Administration / Access Rights`
   - **Important**: Do NOT select any other groups
4. Save

### Step 3: Test Limited Admin Access
1. **Logout** from current session
2. **Login** as `<EMAIL>`
3. Go to `Settings > Users & Companies > Users`
4. Click `Create`

### Step 4: Verify Field Restrictions
**Expected Behavior:**
- ✅ **Name field**: Should be editable
- ✅ **Email field**: Should be editable  
- 🔒 **Groups field**: Should be readonly/disabled
- 🔒 **Active checkbox**: Should be readonly/disabled
- 🔒 **Language dropdown**: Should be readonly/disabled
- 🔒 **Company field**: Should be readonly/disabled
- ℹ️ **Blue info box**: Should appear saying "Limited Admin Mode"

### Step 5: Test User Creation
1. Fill in:
   - **Name**: `New Test User`
   - **Email**: `<EMAIL>`
2. **Save**
3. **Verify**:
   - User created successfully
   - Login automatically set to `<EMAIL>`
   - No groups assigned to new user

## 🔍 If Test Fails

### Check 1: Module Installation
```bash
# Check if module is installed
grep -r "user_creation_limited_rights" /var/log/odoo/odoo.log
```

### Check 2: User Permissions
1. Login as admin
2. Go to test user
3. Check `Access Rights` tab
4. Ensure ONLY `Administration / Access Rights` is selected

### Check 3: Browser Issues
1. Clear browser cache (Ctrl+Shift+Delete)
2. Try incognito/private window
3. Hard refresh (Ctrl+F5)

### Check 4: Server Logs
```bash
# Monitor logs while testing
tail -f /var/log/odoo/odoo.log | grep -i "limited\|admin\|user"
```

## 🛠️ Quick Fixes

### Fix 1: Module Not Working
```bash
# Restart Odoo and update module
sudo systemctl restart odoo
# Then: Apps > User Creation Limited Rights > Upgrade
```

### Fix 2: All Fields Editable
1. **Double-check user groups**:
   - Remove ALL groups from test user
   - Add ONLY `Administration / Access Rights`
   - Save user

2. **Clear cache and retry**

### Fix 3: Cannot Access Users Menu
- User needs `Administration / Access Rights` permission
- Check if user is active

## ✅ Success Indicators

When working correctly, you should see:

1. **Blue info message** appears for limited admin users
2. **Groups field is grayed out** and cannot be clicked
3. **Active checkbox is disabled**
4. **Only Name and Email are editable**
5. **User creation works** with limited fields
6. **Server logs show** "limited admin rights" messages

## 📋 Quick Checklist

- [ ] Module installed and server restarted
- [ ] Test user has ONLY `Administration / Access Rights`
- [ ] Test user can access Users menu
- [ ] Blue info message appears
- [ ] Groups field is readonly
- [ ] Name and Email fields are editable
- [ ] User creation works
- [ ] New users have no groups assigned

## 🎯 Expected Result

**Limited Admin User Experience:**
```
┌─────────────────────────────────────┐
│ 📝 Create User Form                 │
├─────────────────────────────────────┤
│ ℹ️  Limited Admin Mode: You can     │
│    only modify Name and Email       │
│    Address fields...                │
├─────────────────────────────────────┤
│ Name: [Editable Text Field]         │
│ Email: [Editable Text Field]        │
│ Groups: [Readonly/Grayed Out]       │
│ Active: [Readonly Checkbox]         │
│ Language: [Readonly Dropdown]       │
│ Company: [Readonly Field]           │
└─────────────────────────────────────┘
```

If you see this interface, the module is working correctly! 🎉
