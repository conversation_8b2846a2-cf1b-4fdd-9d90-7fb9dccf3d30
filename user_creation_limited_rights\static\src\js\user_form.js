odoo.define('user_creation_limited_rights.user_form', function (require) {
'use strict';

var FormController = require('web.FormController');
var FormView = require('web.FormView');
var viewRegistry = require('web.view_registry');

var UserFormController = FormController.extend({
    
    /**
     * Override to apply limited admin restrictions on user form
     */
    _onFieldChanged: function (event) {
        var self = this;
        var result = this._super.apply(this, arguments);
        
        // Check if this is a user form and user has limited admin rights
        if (this.modelName === 'res.users') {
            this._applyLimitedAdminRestrictions();
        }
        
        return result;
    },
    
    /**
     * Apply field restrictions for limited admin users
     */
    _applyLimitedAdminRestrictions: function () {
        var self = this;
        var record = this.model.get(this.handle);
        
        if (record && record.data.has_limited_admin_rights) {
            // List of fields that should be readonly for limited admin users
            var readonlyFields = [
                'active', 'groups_id', 'lang', 'tz', 'company_id', 
                'company_ids', 'login', 'phone', 'mobile', 'signature', 
                'action_id', 'share', 'partner_id', 'image_1920'
            ];
            
            // Apply readonly to fields
            readonlyFields.forEach(function (fieldName) {
                var field = self.renderer.allFieldWidgets[record.id] && 
                           self.renderer.allFieldWidgets[record.id][fieldName];
                if (field) {
                    field.setReadonly(true);
                }
            });
            
            // Show info message
            this._showLimitedAdminMessage();
        }
    },
    
    /**
     * Show information message for limited admin users
     */
    _showLimitedAdminMessage: function () {
        if (!this._limitedAdminMessageShown) {
            this.displayNotification({
                title: 'Limited Admin Mode',
                message: 'You can only modify Name and Email Address fields. Other fields are managed by system administrators.',
                type: 'info',
                sticky: false
            });
            this._limitedAdminMessageShown = true;
        }
    }
});

var UserFormView = FormView.extend({
    config: _.extend({}, FormView.prototype.config, {
        Controller: UserFormController,
    }),
});

// Register the view only for res.users model
viewRegistry.add('user_limited_form', UserFormView);

return {
    UserFormController: UserFormController,
    UserFormView: UserFormView,
};

});
