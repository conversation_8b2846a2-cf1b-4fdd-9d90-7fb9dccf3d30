# Installation and Setup Guide

## Prerequisites

- Odoo 15 Community Edition
- Administrator access to Odoo
- Basic understanding of Odoo user management

## Installation Steps

### 1. Module Installation

1. **Copy Module Files**
   ```bash
   cp -r user_creation_limited_rights /path/to/odoo/addons/
   ```

2. **Restart Odoo Server**
   ```bash
   sudo systemctl restart odoo
   # or
   python3 odoo-bin -c /path/to/odoo.conf
   ```

3. **Update Apps List**
   - Go to `Settings > Apps`
   - Click "Update Apps List"
   - Search for "User Creation Limited Rights"
   - Click "Install"

### 2. User Configuration

#### Creating Limited Admin Users

1. **Navigate to Users**
   - Go to `Settings > Users & Companies > Users`

2. **Create New User**
   - Click "Create"
   - Fill in user details
   - **Important**: Only assign `Access Rights` group
   - Do NOT assign `Settings` or `Technical` groups

3. **Verify Permissions**
   - User should have `Administration / Access Rights` only
   - User should NOT have `Administration / Settings`
   - User should NOT have `Technical Features`

#### Testing Limited Admin Access

1. **Login as Limited Admin User**
   - Use the credentials of the newly created limited admin user

2. **Test User Creation**
   - Go to `Settings > Users & Companies > Users`
   - Click "Create"
   - Notice that only `Name` and `Login` fields are editable
   - All other fields should be readonly

3. **Verify Security**
   - Try to modify groups via browser developer tools
   - Changes should be ignored by the backend
   - Check server logs for security warnings

## Configuration Examples

### Example 1: HR Manager with Limited Rights

```python
# Create HR Manager with limited user creation rights
hr_manager = env['res.users'].create({
    'name': 'HR Manager',
    'login': '<EMAIL>',
    'groups_id': [(6, 0, [
        env.ref('base.group_user').id,  # Access Rights
        env.ref('hr.group_hr_manager').id,  # HR Manager
        # Note: No base.group_system or base.group_erp_manager
    ])]
})
```

### Example 2: Department Admin

```python
# Create Department Admin with user creation capabilities
dept_admin = env['res.users'].create({
    'name': 'Department Admin',
    'login': '<EMAIL>',
    'groups_id': [(6, 0, [
        env.ref('base.group_user').id,  # Access Rights only
    ])]
})
```

## Verification Checklist

### ✅ Installation Verification

- [ ] Module appears in Apps list
- [ ] Module installs without errors
- [ ] No error messages in server logs
- [ ] User form views load correctly

### ✅ Functionality Verification

- [ ] Limited admin users can access user creation form
- [ ] Only `name` and `login` fields are editable
- [ ] Groups field is readonly
- [ ] Active field is readonly
- [ ] Language and timezone fields are readonly
- [ ] User creation works with allowed fields
- [ ] Unauthorized field modifications are blocked

### ✅ Security Verification

- [ ] Group assignments are blocked for limited admin users
- [ ] Backend API calls respect restrictions
- [ ] Security violations are logged
- [ ] Full admin users retain all capabilities
- [ ] No privilege escalation possible

## Troubleshooting

### Common Issues

1. **Module Not Appearing in Apps List**
   - Check file permissions
   - Restart Odoo server
   - Update apps list
   - Check server logs for errors

2. **All Fields Still Editable**
   - Verify user has only `base.group_user`
   - Clear browser cache
   - Check for conflicting modules
   - Restart Odoo server

3. **Cannot Create Users**
   - Ensure user has `base.group_user` permission
   - Check access rights in security settings
   - Verify module installation

4. **Security Warnings in Logs**
   - This is normal behavior when unauthorized access is attempted
   - Review logs to identify potential security issues
   - Educate users about proper usage

### Debug Mode

Enable developer mode to see additional information:

1. Go to `Settings > Developer Tools > Activate Developer Mode`
2. Check computed field values in user forms
3. Monitor field attributes and readonly states
4. Review security logs in real-time

### Log Monitoring

Monitor Odoo logs for security-related messages:

```bash
tail -f /var/log/odoo/odoo.log | grep "Limited admin user"
```

## Support

For technical support:

1. Check the module's README.md file
2. Review server logs for error messages
3. Test with different user permission combinations
4. Verify Odoo version compatibility

## Uninstallation

To remove the module:

1. Go to `Settings > Apps`
2. Search for "User Creation Limited Rights"
3. Click "Uninstall"
4. Restart Odoo server (recommended)

**Note**: Uninstalling will restore default user creation behavior.
