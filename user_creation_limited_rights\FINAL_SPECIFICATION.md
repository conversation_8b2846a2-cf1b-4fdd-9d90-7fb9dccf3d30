# Final Specification - User Creation Limited Rights

## 🎯 Exact Requirements Implementation

### User Permission Level Definition
**Limited Admin User** = User who has:
- ✅ **Administration / Access Rights** (`base.group_user`)
- ❌ **NOT** Administration / Settings (`base.group_system`)
- ❌ **NOT** ERP Manager (`base.group_erp_manager`)

### Field Access Control

#### ✅ Editable Fields (Limited Admin)
1. **Name** (`name`) - User display name
2. **Email Address** (`email`) - User email address

#### 🔒 Readonly Fields (Limited Admin)
1. **Groups** (`groups_id`) - User permissions/access rights
2. **Active** (`active`) - User activation status
3. **Login** (`login`) - Automatically set from email
4. **Language** (`lang`) - User interface language
5. **Timezone** (`tz`) - User timezone
6. **Company** (`company_id`) - User company
7. **Companies** (`company_ids`) - Multi-company access
8. **Phone** (`phone`) - Phone number
9. **Mobile** (`mobile`) - Mobile number
10. **Signature** (`signature`) - User signature
11. **Action** (`action_id`) - Default action
12. **Share** (`share`) - Portal user flag
13. **Partner** (`partner_id`) - Related partner
14. **Image** (`image_1920`) - User avatar
15. **Notification Type** (`notification_type`) - Email preferences

### Automatic Behavior
- **Login Auto-Set**: When user enters email, login is automatically set to the same value
- **Security Filtering**: Any attempt to modify readonly fields is silently ignored
- **Group Protection**: Groups assignment is blocked and logged for security

### User Interface
- **Info Message**: Blue alert box appears for limited admin users explaining restrictions
- **Visual Indication**: Readonly fields are visually disabled
- **Clean UX**: No error messages, just silent field protection

### Backend Security
- **API Protection**: XMLRPC/JSON-RPC calls respect field restrictions
- **Audit Logging**: All unauthorized attempts are logged with user details
- **Multi-layer Validation**: Model, view, and constraint level protection
- **Privilege Escalation Prevention**: Cannot assign admin groups

## 🔧 Technical Implementation

### Permission Check Logic
```python
def _is_limited_admin_user(self):
    current_user = self.env.user
    has_access_rights = current_user.has_group('base.group_user')
    has_settings = current_user.has_group('base.group_system')
    has_erp_manager = current_user.has_group('base.group_erp_manager')
    
    return (has_access_rights and 
            not has_settings and 
            not has_erp_manager and
            not self.env.is_superuser())
```

### Field Control Logic
```python
def _get_allowed_fields_for_limited_admin(self):
    return ['name', 'email']
```

### Auto-Login Setting
```python
if 'email' in vals and 'login' not in vals:
    vals['login'] = vals['email']
```

## 🛡️ Security Matrix

| Action | Limited Admin | Full Admin | Regular User |
|--------|---------------|------------|--------------|
| View Users | ✅ Yes | ✅ Yes | ❌ No |
| Create Users | ✅ Yes | ✅ Yes | ❌ No |
| Edit Name | ✅ Yes | ✅ Yes | ❌ No |
| Edit Email | ✅ Yes | ✅ Yes | ❌ No |
| Edit Groups | ❌ No | ✅ Yes | ❌ No |
| Edit Active | ❌ No | ✅ Yes | ❌ No |
| Edit Language | ❌ No | ✅ Yes | ❌ No |
| Edit Company | ❌ No | ✅ Yes | ❌ No |
| Delete Users | ❌ No | ✅ Yes | ❌ No |

## 📋 Use Case Examples

### Example 1: HR Manager
```
User: HR Manager
Groups: Administration/Access Rights + Human Resources/Manager
Scenario: Creating new employee user account
Can Do: Set name and email for new employee
Cannot Do: Assign admin rights or technical permissions
Result: Secure user creation for HR workflows
```

### Example 2: Department Manager
```
User: Sales Manager  
Groups: Administration/Access Rights + Sales/Manager
Scenario: Adding new team member
Can Do: Create basic user account with name and email
Cannot Do: Modify system settings or assign groups
Result: Controlled user creation for department needs
```

### Example 3: IT Support Level 1
```
User: IT Support
Groups: Administration/Access Rights (only)
Scenario: Creating user accounts for new hires
Can Do: Basic user account creation
Cannot Do: Assign technical or admin privileges
Result: Safe user creation without security risks
```

## 🔍 Verification Process

### Step 1: Create Limited Admin User
1. Go to Settings > Users & Companies > Users
2. Create new user with:
   - Name: Test Limited Admin
   - Email: <EMAIL>
   - Groups: **Only** "Administration / Access Rights"
3. Save user

### Step 2: Test Limited Admin Access
1. Logout and <NAME_EMAIL>
2. Go to Settings > Users & Companies > Users
3. Click "Create"
4. Verify:
   - ✅ Name field is editable
   - ✅ Email field is editable
   - 🔒 Groups field is readonly
   - 🔒 Active field is readonly
   - 🔒 All other fields are readonly
   - ℹ️ Blue info message appears

### Step 3: Test User Creation
1. Fill in:
   - Name: "New Test User"
   - Email: "<EMAIL>"
2. Save user
3. Verify:
   - ✅ User created successfully
   - ✅ Login automatically set to "<EMAIL>"
   - ✅ No groups assigned
   - ✅ Default values for other fields

### Step 4: Test Security
1. Try to modify Groups field (should be readonly)
2. Check server logs for any security warnings
3. Test with full admin user (should have full access)

## ✅ Success Criteria

- [x] Limited admin users can create users
- [x] Only name and email fields are editable
- [x] Groups field is completely readonly
- [x] Login automatically set from email
- [x] Backend security prevents API manipulation
- [x] Full admin users retain all capabilities
- [x] Security violations are logged
- [x] Clean user interface with clear messaging
- [x] No errors during user creation process
- [x] Compatible with Odoo 15 Community Edition

## 🎉 Final Status: COMPLETE ✅

The module now exactly implements the specified requirements:
- Users with "Access Rights" only (no "Settings") can create users
- Only Name and Email Address fields are editable
- All other fields including Groups/Permissions are readonly
- Login is automatically set from email address
- Full security protection at all levels
- Clean and intuitive user interface
