# Changelog

## Version 15.0.1.0.0 - 2025-01-04

### Added
- Initial release of User Creation Limited Rights module
- Limited admin user creation capabilities
- Backend security protection
- Field-level access control
- Security logging and monitoring
- Comprehensive test suite

### Features
- **Limited Admin Support**: Users with `base.group_user` can create users with restricted field access
- **Field Restrictions**: Only `name` and `login` fields editable for limited admin users
- **Backend Protection**: Prevents unauthorized API manipulation
- **Security Logging**: All unauthorized attempts are logged
- **User-Friendly Interface**: Clear indication of limited admin mode

### Security
- **Group Protection**: Prevents unauthorized group assignments
- **API Security**: Backend validation against XMLRPC/JSON-RPC manipulation
- **Privilege Escalation Prevention**: Multiple validation layers
- **Audit Trail**: Comprehensive logging of security events

### Technical Details
- **Model Extension**: Extends `res.users` model with security controls
- **View Inheritance**: Minimal view modifications for better compatibility
- **Dynamic Field Control**: Uses `fields_get()` override for field-level security
- **Constraint Validation**: Additional constraints for data integrity

### Files Added
- `models/res_users.py` - Core security logic
- `views/res_users_views.xml` - User interface modifications
- `security/ir.model.access.csv` - Access rights configuration
- `tests/test_user_creation_limited_rights.py` - Comprehensive test suite
- Documentation files (README.md, INSTALL.md, SUMMARY.md)

### Compatibility
- ✅ Odoo 15 Community Edition
- ✅ Compatible with standard Odoo modules
- ✅ No external dependencies
- ✅ Clean upgrade/uninstall process

### Known Issues
- None reported

### Migration Notes
- First release - no migration required
- Clean installation process
- No data migration needed

---

## Future Versions

### Planned Features (v15.*******)
- Configurable field permissions
- Role-based access control
- Enhanced logging dashboard
- Integration with audit modules

### Potential Enhancements
- Custom permission groups
- Field-level permission matrix
- Advanced user creation workflows
- Integration with HR modules

---

## Support Information

### Reporting Issues
- Check logs for security violations
- Verify user group memberships
- Test with different permission levels
- Review module installation

### Debug Information
- Enable developer mode for detailed field information
- Monitor server logs for security events
- Use test suite for validation
- Check computed field values

### Contact
For technical support and feature requests, please refer to the module documentation.
