# Installation Fix - User Creation Limited Rights

## 🔧 Fixed Installation Issues

### Problem Resolved
The XML view inheritance errors have been fixed. The module now uses a simpler approach that relies primarily on backend protection rather than complex view inheritance.

### Changes Made

#### 1. Simplified Views (`views/res_users_views.xml`)
- Removed problematic xpath expressions for non-existent fields
- Kept only the essential computed field and info message
- Backend protection now handles field restrictions

#### 2. Enhanced Backend Protection (`models/res_users.py`)
- Improved `fields_get()` method to make ALL fields readonly except allowed ones
- Added comprehensive logging for debugging
- Enhanced field validation and security

#### 3. Added JavaScript Support (`static/src/js/user_form.js`)
- Client-side field restriction enforcement
- User-friendly notifications
- Dynamic field control

#### 4. Assets Integration (`views/assets.xml`)
- Proper JavaScript loading
- Web assets integration

## 🚀 Installation Steps

### Step 1: Clean Installation
```bash
# Stop Odoo server
sudo systemctl stop odoo

# Remove any previous installation
rm -rf /path/to/addons/user_creation_limited_rights

# Copy new module
cp -r user_creation_limited_rights /path/to/addons/

# Set proper permissions
chown -R odoo:odoo /path/to/addons/user_creation_limited_rights
chmod -R 755 /path/to/addons/user_creation_limited_rights

# Start Odoo server
sudo systemctl start odoo
```

### Step 2: Install Module
1. Go to `Apps` menu
2. Click "Update Apps List"
3. Search for "User Creation Limited Rights"
4. Click "Install"

### Step 3: Verify Installation
```bash
# Check logs for successful installation
tail -f /var/log/odoo/odoo.log | grep -i "user_creation_limited_rights"
```

## 🧪 Testing Instructions

### Create Test User
1. Go to `Settings > Users & Companies > Users`
2. Create new user:
   - **Name**: Test Limited Admin
   - **Email**: <EMAIL>
   - **Groups**: ONLY `Administration / Access Rights`
3. Save user

### Test Limited Access
1. **Logout** and **login** as <EMAIL>
2. Go to `Settings > Users & Companies > Users`
3. Click `Create`

### Expected Behavior
- ✅ **Name field**: Editable
- ✅ **Email field**: Editable
- 🔒 **All other fields**: Readonly (grayed out)
- ℹ️ **Blue info box**: Shows "Limited Admin Mode" message
- 🔔 **Notification**: May show info notification about restrictions

### Verify Field Restrictions
Try to:
- Click on Groups field → Should be disabled
- Change Active checkbox → Should be disabled
- Modify Language dropdown → Should be disabled
- Edit Company field → Should be disabled

## 🔍 Troubleshooting

### Issue 1: Module Won't Install
**Solution:**
```bash
# Check for syntax errors
python3 -c "import ast; ast.parse(open('models/res_users.py').read())"

# Check XML syntax
xmllint --noout views/res_users_views.xml
```

### Issue 2: Fields Still Editable
**Possible Causes:**
- User has additional permissions
- Browser cache issues
- Module not properly loaded

**Solutions:**
1. **Verify User Groups:**
   - Remove ALL groups from test user
   - Add ONLY `Administration / Access Rights`

2. **Clear Cache:**
   ```bash
   # Clear browser cache
   # Hard refresh (Ctrl+F5)
   # Try incognito window
   ```

3. **Restart Services:**
   ```bash
   sudo systemctl restart odoo
   ```

### Issue 3: JavaScript Not Working
**Solution:**
```bash
# Check if assets are loaded
# In browser console, check for JavaScript errors
# Verify assets.xml is properly loaded
```

## 📊 Debug Information

### Check User Permissions
```python
# In Odoo shell or debug mode
user = env.user
print(f"User: {user.name}")
print(f"Groups: {[g.name for g in user.groups_id]}")
print(f"Has base.group_user: {user.has_group('base.group_user')}")
print(f"Has base.group_system: {user.has_group('base.group_system')}")
```

### Check Field Restrictions
```python
# Test fields_get method
fields_info = env['res.users'].fields_get(['name', 'email', 'groups_id', 'active'])
for field, info in fields_info.items():
    print(f"{field}: readonly = {info.get('readonly', False)}")
```

### Monitor Logs
```bash
# Watch for module activity
tail -f /var/log/odoo/odoo.log | grep -E "(Limited admin|fields_get|has_limited_admin_rights)"
```

## ✅ Success Indicators

When working correctly:

1. **Installation**: No errors in logs during installation
2. **User Interface**: Blue info message appears for limited admin users
3. **Field Behavior**: Only Name and Email are editable
4. **Backend Logs**: Show "Limited admin user" messages
5. **User Creation**: Works with restricted fields only
6. **Security**: Groups cannot be modified

## 📞 Support

If issues persist:

1. **Collect Information:**
   - Installation logs
   - User group assignments
   - Browser console errors
   - Server error logs

2. **Verify Environment:**
   - Odoo 15 Community Edition
   - Proper file permissions
   - Module in correct addons path

3. **Test Systematically:**
   - Fresh user creation
   - Clean browser session
   - Different user permission levels

The module should now install and work correctly without XML inheritance errors.
