# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import AccessError, ValidationError


class TestUserCreationLimitedRights(TransactionCase):

    def setUp(self):
        super(TestUserCreationLimitedRights, self).setUp()
        
        # Create test groups
        self.group_user = self.env.ref('base.group_user')
        self.group_system = self.env.ref('base.group_system')
        
        # Create a limited admin user (has base.group_user but not system)
        self.limited_admin = self.env['res.users'].create({
            'name': 'Limited Admin Test User',
            'login': '<EMAIL>',
            'groups_id': [(6, 0, [self.group_user.id])]
        })
        
        # Create a full admin user
        self.full_admin = self.env['res.users'].create({
            'name': 'Full Admin Test User',
            'login': '<EMAIL>',
            'groups_id': [(6, 0, [self.group_user.id, self.group_system.id])]
        })

    def test_limited_admin_rights_computation(self):
        """Test that has_limited_admin_rights is computed correctly"""
        
        # Test with limited admin user
        limited_user_record = self.env['res.users'].with_user(self.limited_admin).browse(self.limited_admin.id)
        limited_user_record._compute_has_limited_admin_rights()
        self.assertTrue(limited_user_record.has_limited_admin_rights)
        
        # Test with full admin user
        full_admin_record = self.env['res.users'].with_user(self.full_admin).browse(self.full_admin.id)
        full_admin_record._compute_has_limited_admin_rights()
        self.assertFalse(full_admin_record.has_limited_admin_rights)

    def test_limited_admin_user_creation(self):
        """Test that limited admin users can create users with restricted fields"""
        
        # Test creating user as limited admin
        user_vals = {
            'name': 'Test User Created by Limited Admin',
            'email': '<EMAIL>',
            'groups_id': [(6, 0, [self.group_system.id])],  # This should be removed
            'active': False,  # This should be removed
            'lang': 'fr_FR',  # This should be removed
        }

        new_user = self.env['res.users'].with_user(self.limited_admin).create(user_vals)

        # Check that the user was created
        self.assertTrue(new_user.exists())
        self.assertEqual(new_user.name, 'Test User Created by Limited Admin')
        self.assertEqual(new_user.email, '<EMAIL>')
        
        # Check that unauthorized fields were not set
        self.assertNotIn(self.group_system.id, new_user.groups_id.ids)
        # Note: active and lang might have default values, so we don't test them here

    def test_limited_admin_user_modification(self):
        """Test that limited admin users cannot modify unauthorized fields"""
        
        # Create a test user first
        test_user = self.env['res.users'].create({
            'name': 'Test User for Modification',
            'login': '<EMAIL>',
        })
        
        # Try to modify as limited admin
        modification_vals = {
            'name': 'Modified Name',  # This should work
            'groups_id': [(6, 0, [self.group_system.id])],  # This should be removed
            'active': False,  # This should be removed
        }
        
        test_user.with_user(self.limited_admin).write(modification_vals)
        
        # Check that allowed field was modified
        self.assertEqual(test_user.name, 'Modified Name')
        
        # Check that unauthorized fields were not modified
        self.assertNotIn(self.group_system.id, test_user.groups_id.ids)

    def test_full_admin_unrestricted_access(self):
        """Test that full admin users have unrestricted access"""
        
        # Test creating user as full admin
        user_vals = {
            'name': 'Test User Created by Full Admin',
            'login': '<EMAIL>',
            'groups_id': [(6, 0, [self.group_system.id])],
            'active': False,
            'lang': 'fr_FR',
        }
        
        new_user = self.env['res.users'].with_user(self.full_admin).create(user_vals)
        
        # Check that all fields were set correctly
        self.assertEqual(new_user.name, 'Test User Created by Full Admin')
        self.assertEqual(new_user.login, '<EMAIL>')
        self.assertIn(self.group_system.id, new_user.groups_id.ids)
        self.assertFalse(new_user.active)

    def test_allowed_fields_for_limited_admin(self):
        """Test the _get_allowed_fields_for_limited_admin method"""

        allowed_fields = self.env['res.users']._get_allowed_fields_for_limited_admin()
        expected_fields = ['name', 'email']

        self.assertEqual(set(allowed_fields), set(expected_fields))

    def test_is_limited_admin_user_method(self):
        """Test the _is_limited_admin_user method"""
        
        # Test with limited admin
        result = self.env['res.users'].with_user(self.limited_admin)._is_limited_admin_user()
        self.assertTrue(result)
        
        # Test with full admin
        result = self.env['res.users'].with_user(self.full_admin)._is_limited_admin_user()
        self.assertFalse(result)

    def test_user_creation_rights_check(self):
        """Test the _check_user_creation_rights method"""
        
        # Test with limited admin
        result = self.env['res.users'].with_user(self.limited_admin)._check_user_creation_rights()
        self.assertTrue(result)
        
        # Test with full admin
        result = self.env['res.users'].with_user(self.full_admin)._check_user_creation_rights()
        self.assertTrue(result)
        
        # Test with user without rights (create a user without base.group_user)
        no_rights_user = self.env['res.users'].create({
            'name': 'No Rights User',
            'login': '<EMAIL>',
            'groups_id': [(5, 0, 0)]  # Remove all groups
        })
        
        result = self.env['res.users'].with_user(no_rights_user)._check_user_creation_rights()
        self.assertFalse(result)
