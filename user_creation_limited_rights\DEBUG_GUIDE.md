# Debug Guide - User Creation Limited Rights

## 🔍 Troubleshooting Steps

### Step 1: Verify Module Installation

1. **Check Module Status:**
   - Go to `Apps` menu
   - Search for "User Creation Limited Rights"
   - Verify status shows "Installed"

2. **Check Server Logs:**
   ```bash
   tail -f /var/log/odoo/odoo.log | grep "Limited admin"
   ```

### Step 2: Verify User Permissions

1. **Check User Groups:**
   - Go to `Settings > Users & Companies > Users`
   - Open the test user
   - Go to `Access Rights` tab
   - Verify user has ONLY:
     - ✅ `Administration / Access Rights`
   - Verify user does NOT have:
     - ❌ `Administration / Settings`
     - ❌ `Technical Features`

2. **Debug User Permissions:**
   ```python
   # In Odoo shell or debug mode
   user = env['res.users'].browse(USER_ID)
   print("User:", user.name)
   print("Has base.group_user:", user.has_group('base.group_user'))
   print("Has base.group_system:", user.has_group('base.group_system'))
   print("Has base.group_erp_manager:", user.has_group('base.group_erp_manager'))
   ```

### Step 3: Test Limited Admin Detection

1. **Login as Limited Admin User**
2. **Open User Creation Form:**
   - Go to `Settings > Users & Companies > Users`
   - Click `Create`

3. **Check Debug Info:**
   - Enable Developer Mode
   - Check browser console for any JavaScript errors
   - Check server logs for debug messages

### Step 4: Manual Field Check

1. **Inspect Form Fields:**
   - Right-click on Groups field
   - Select "Inspect Element"
   - Check if `readonly` attribute is present

2. **Test Field Modification:**
   - Try to click on Groups field
   - Try to modify Active checkbox
   - Try to change Language dropdown

### Step 5: Backend Testing

1. **Test API Protection:**
   ```python
   # Try to create user with groups via API
   user_vals = {
       'name': 'Test User',
       'email': '<EMAIL>',
       'groups_id': [(6, 0, [env.ref('base.group_system').id])]
   }
   
   # This should remove groups_id for limited admin users
   new_user = env['res.users'].create(user_vals)
   print("Created user groups:", new_user.groups_id.mapped('name'))
   ```

## 🔧 Common Issues and Solutions

### Issue 1: All Fields Are Still Editable

**Possible Causes:**
- Module not properly installed
- User has additional permissions
- Browser cache issues

**Solutions:**
1. **Restart Odoo Server:**
   ```bash
   sudo systemctl restart odoo
   ```

2. **Update Module:**
   - Go to `Apps`
   - Search for the module
   - Click `Upgrade`

3. **Clear Browser Cache:**
   - Hard refresh (Ctrl+F5)
   - Clear browser cache completely

4. **Check User Groups Again:**
   - Remove ALL groups from user
   - Add ONLY `Administration / Access Rights`

### Issue 2: Cannot Create Users

**Possible Causes:**
- User doesn't have `base.group_user`
- Access rights not configured properly

**Solutions:**
1. **Verify Access Rights:**
   - User must have `Administration / Access Rights`

2. **Check Security Rules:**
   - Verify `security/ir.model.access.csv` is loaded

### Issue 3: Module Installation Fails

**Possible Causes:**
- View inheritance errors
- Python syntax errors
- Missing dependencies

**Solutions:**
1. **Check Server Logs:**
   ```bash
   tail -f /var/log/odoo/odoo.log
   ```

2. **Validate XML Files:**
   - Check for syntax errors in views
   - Verify xpath expressions are correct

3. **Test Python Code:**
   ```bash
   python3 -m py_compile models/res_users.py
   ```

## 🧪 Testing Checklist

### Pre-Test Setup
- [ ] Module installed successfully
- [ ] Server restarted after installation
- [ ] Test user created with correct permissions
- [ ] Browser cache cleared

### Limited Admin User Test
- [ ] User has only `Administration / Access Rights`
- [ ] User can access Users menu
- [ ] User can click "Create" button
- [ ] Blue info message appears
- [ ] Only Name and Email fields are editable
- [ ] Groups field is readonly
- [ ] Active field is readonly
- [ ] Language field is readonly
- [ ] Company field is readonly

### User Creation Test
- [ ] Can enter Name
- [ ] Can enter Email
- [ ] Cannot modify Groups
- [ ] Cannot change Active status
- [ ] Save button works
- [ ] User created successfully
- [ ] Login set from email
- [ ] No groups assigned

### Security Test
- [ ] Try to modify Groups via browser dev tools (should fail)
- [ ] Check server logs for security warnings
- [ ] Test with full admin user (should work normally)

## 📊 Debug Commands

### Check User Permissions
```python
# In Odoo shell
user = env.user
print(f"Current user: {user.name}")
print(f"Groups: {user.groups_id.mapped('name')}")
print(f"Has limited rights: {user.has_group('base.group_user') and not user.has_group('base.group_system')}")
```

### Check Field Attributes
```python
# Check fields_get output
fields_info = env['res.users'].fields_get(['groups_id', 'active', 'name', 'email'])
for field, info in fields_info.items():
    print(f"{field}: readonly = {info.get('readonly', False)}")
```

### Test Module Functions
```python
# Test limited admin detection
users_obj = env['res.users']
print(f"Is limited admin: {users_obj._is_limited_admin_user()}")
print(f"Allowed fields: {users_obj._get_allowed_fields_for_limited_admin()}")
```

## 📞 Support Information

If the module still doesn't work after following this guide:

1. **Collect Debug Information:**
   - Server logs during user creation
   - User group assignments
   - Browser console errors
   - Module installation status

2. **Verify Environment:**
   - Odoo version (must be 15.x)
   - Module file permissions
   - Database connection

3. **Test with Clean User:**
   - Create completely new user
   - Assign only required permission
   - Test in incognito browser window

The module should work correctly if all steps are followed properly.
